import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uuid_util.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import 'package:wef_network/wef_request.dart';

const String prodUrl = 'https://waimaieapp.meituan.com';

/// SSE事件模型
class SSEModel {
  String data = '';
  Map<String, dynamic> extraData = {};

  SSEModel({this.data = '', Map<String, dynamic> extraData})
      : extraData = extraData ?? {};
}

/// 部分业务会用到这个函数
getCommParams() {
  if (PlatformTools.isPC) {
    return {
      "acctId": Util.getUrlParam('acctId') ?? Util.getCookie('acctId'),
      "wmPoiId": Util.getUrlParam('wmPoiIds') ??
          Util.getUrlParam('wmPoiId') ??
          Util.getCookie('wmPoiId'),
      "token": Util.getUrlParam('token') ?? Util.getCookie('token'),
      "appType": Util.getAppType(),
    };
  }
  return {};
}

/// 获取扩展的通用参数，包含环境信息
Future<Map<String, dynamic>> getExtendedCommParams() async {
  // 获取环境信息
  final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
  final poiInfo = await WaimaiENativeBusiness.getPoiInfo();
  final userInfo = await WaimaiENativeBusiness.getUserInfo();
  Map<String, dynamic> baseParams = Map<String, dynamic>.from(getCommParams());

  // 添加扩展参数
  if (envInfo != null) {
    baseParams['wm_appversion'] = envInfo['appVersion'];
    baseParams['hostType'] = envInfo['hostType'];
    // 尝试从环境信息中获取 userAgent，如果没有则使用默认值
    baseParams['userAgent'] = envInfo['userAgent'] ??
        'com.meituan.itakeawaybiz/31086 (unknown, iOS 17.2, iPhone, Scale/3.000000)';
  }

  if (poiInfo != null) {
    baseParams['wmPoiId'] = poiInfo.poiId;
  }

  if (userInfo != null) {
    baseParams['acctId'] = userInfo.acctId;
    // 移动端从userInfo获取token
    if (userInfo.accessToken != null) {
      baseParams['token'] = userInfo.accessToken;
    }
  }

  // 添加平台信息
  baseParams['appType'] = Platform.isAndroid ? '4' : '5';
  return baseParams;
}

/// 流式接口请求，返回 Stream<String>，直接返回数据内容
Stream<String> comStreamApi({
  String path,
  Map params,
  void Function(dynamic error) onError,
}) {
  final controller = StreamController<String>.broadcast();

  _initializeStreamRequest(
    controller,
    path,
    params,
    onError
  );

  return controller.stream;
}

/// 初始化流式请求
Future<void> _initializeStreamRequest(
  StreamController<String> controller,
  String path,
  Map params,
  void Function(dynamic error) onError,
) async {
  Dio dio;
  String globalBuffer = '';

  final extendedParams = await getExtendedCommParams();
  
  // 正则表达式，用于匹配 data: 开头的 JSON 数据
  final RegExp dataJsonRegex = RegExp(r'data:\s*({.*?})(?=\s*data:|\n|$)', dotAll: true);

  // 常量配置
  const String sseEndMarker = '[DONE]';
  const int connectionTimeout = 30000;
  const int receiveTimeout = 120000;
  const int sendTimeout = 120000;

  String hostUrlApi = 'https://eapi.waimai.meituan.com';
  String hostType = extendedParams['hostType'];
  if (hostType == 'TEST' || hostType == 'QA') {
    hostUrlApi = "http://eapi.b.waimai.test.sankuai.com";
  } else if (hostType == 'STAGE') {
    hostUrlApi = "https://eapi.waimai.st.sankuai.com";
  }

  // 准备请求头
  final Map<String, String> headers = {
    'contentType': 'application/json',
    'contentEncoding': 'gzip',
    "accept": "text/event-stream",
    "cache-control": "no-cache",
    "connection": "keep-alive",
    'userAgent': extendedParams['userAgent'] ??
        'com.meituan.itakeawaybiz/31086 (unknown, iOS 17.2, iPhone, Scale/3.000000)',
  };

  // 准备请求体
  String requestId = await UuidUtil().getUuidWithHourCache();
  final Map<String, dynamic> requestBody = {
    'appType': extendedParams['appType'] ?? (Platform.isAndroid ? '4' : '5'),
    'token': extendedParams['token'] ?? '',
    'wm_appversion': extendedParams['wm_appversion'] ?? '',
    'wmPoiId': extendedParams['wmPoiId'] ?? '', // 接口必填
    'acctId': extendedParams['acctId'] ?? '',   // 接口必填
    'businessLine': '1',                               // 接口必填，固定值
    'businessMap': {'sceneType': 1},                   // 接口必填
    'requestId': requestId,                            // 接口必填
  };

  // 添加自定义参数
  if (params != null) {
    requestBody.addAll(Map<String, dynamic>.from(params));
  }

  final allowedFields = ['appType', 'token', 'wm_appversion', 'wmPoiId', 'acctId'];
  String queryString = requestBody.entries
      .where((e) => allowedFields.contains(e.key))
      .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
      .join('&');

  String pathWithQuery = '$path?$queryString';
  String fullUrl = hostUrlApi + pathWithQuery;

  // 编码请求体
  String encodedBody = jsonEncode(requestBody);

  // 添加签名
  var result = await KNB.addRequestSignature(
      method: "POST",
      url: fullUrl,
      body: encodedBody,
      header: headers);

  if (result == null || result['status'] != "success") {
    String errorMsg = result != null && result['message'] != null ? result['message'] : 'Failed to process signature';
    onError(errorMsg);
    controller.close();
    return;
  }

  // 处理签名
  dynamic mtgsig = result['mtgsig'];
  if (mtgsig != null) {
    if (mtgsig is String) {
      Map<String, dynamic> parsedMtgsig = jsonDecode(mtgsig);
      if (parsedMtgsig.containsKey('mtgsig')) {
        headers['mtgsig'] = parsedMtgsig['mtgsig'];
      }
    } else if (mtgsig is Map && mtgsig.containsKey('mtgsig')) {
      headers['mtgsig'] = mtgsig['mtgsig'].toString();
    }
  }

  // 配置Dio
  dio = Dio(BaseOptions(
    baseUrl: hostUrlApi,
    responseType: ResponseType.stream,
    headers: headers,
    connectTimeout: connectionTimeout,
    receiveTimeout: receiveTimeout,
    sendTimeout: sendTimeout,
    contentType: "application/json",
  ));

  // 重置缓冲区
  globalBuffer = '';

  // 发起请求
  final response = await dio.post<ResponseBody>(pathWithQuery, data: requestBody);

  if (response.data == null) {
    onError('Response data is null');
    controller.close();
    return;
  }

  // 处理流式数据
  response.data.stream.listen(
    (chunk) {
      _processStreamChunk(chunk, controller, onError, globalBuffer, dataJsonRegex, sseEndMarker);
    },
    onDone: () {
      controller.close();
      _disposeStreamDio(dio);
    },
    onError: (error) {
      onError(error);
      controller.close();
      _disposeStreamDio(dio);
    },
  );
} 

Future<ResponseData> comGetApi({
  String path,
  Map params,
  bool isControlShowToast = false,
  bool useCommonParam = true,
}) {
  return _comBaseApi(
    path: path,
    params: params,
    isControlShowToast: isControlShowToast,
    useCommonParam: useCommonParam,
    method: 'GET',
  );
}

Future<ResponseData> comPostApi({
  String path,
  Map params,
  bool isControlShowToast = false,
  bool useCommonParam = true,
}) {
  return _comBaseApi(
    path: path,
    params: params,
    isControlShowToast: isControlShowToast,
    useCommonParam: useCommonParam,
    method: 'POST',
  );
}

Future<ResponseData> _comBaseApi({
  String path,
  Map params,
  bool isControlShowToast = false,
  // 先富系统不需要通用参数，都是从url上传递过来的
  // 部分业务可能不需要通参
  bool useCommonParam = true,
  String method = 'GET',
}) async {
  String _baseUrl = MTFlutterWebUtils.getApiBaseUrl(ApiConfig('', prodUrl));
  if (!path.startsWith('/')) {
    path = '/$path';
  }

  Map _finalParams = {};
  if (useCommonParam) {
    _finalParams = getCommParams();
  }
  _finalParams.addAll(Map<String, dynamic>.from(params ?? {}));

  handleError(error) {
    if (error is FormatException) {
      Loading.showToast(message: '网络请求超时，请稍后重试');
    } else {
      if (!isControlShowToast) {
        Loading.showToast(
            message: error?.message ?? '网络请求异常，请稍后重试',
            duration: Duration(seconds: 3));
      } else {
        return ResponseData(1, error?.message, null);
      }
    }
  }

  if (method == 'GET') {
    return getApi(
            baseUrl: _baseUrl,
            path: path,
            params: _finalParams,
            isControlShowToast: true)
        .catchError(handleError);
  } else {
    return postApi(
            baseUrl: _baseUrl,
            path: path,
            params: _finalParams,
            isControlShowToast: true)
        .catchError(handleError);
  }
}

/// 处理流式数据块
void _processStreamChunk(
  List<int> chunk,
  StreamController<String> controller,
  void Function(dynamic error) onError,
  String globalBuffer,
  RegExp dataJsonRegex,
  String sseEndMarker,
) {
  try {
    if (chunk == null || chunk.isEmpty) {
      return;
    }

    final chunkData = utf8.decode(chunk);
    if (chunkData.isEmpty) {
      return;
    }

    // 添加到全局缓冲区
    globalBuffer += chunkData;

    // 检查结束标记
    if (globalBuffer.contains(sseEndMarker)) {
      debugPrint('Stream ended with [DONE] marker');
      controller.close();
      return;
    }

    // 检测错误响应
    if (chunkData.startsWith('{') && chunkData.endsWith('}')) {
      try {
        Map<String, dynamic> result = json.decode(chunkData);
        if (result.containsKey("code") && result['code'] != 0) {
          onError(result['msg'] ?? 'Unknown error');
          controller.close();
          return;
        }
      } catch (e) {
        // 不是错误响应，继续处理
      }
    }

    // 从缓冲区提取JSON数据
    List<String> jsonDataList = _extractJsonFromBuffer(globalBuffer, dataJsonRegex);
    for (String jsonData in jsonDataList) {
      try {
        Map<String, dynamic> jsonMap = json.decode(jsonData);

        if (jsonMap.containsKey("code") && jsonMap['code'] != 0) {
          onError(jsonMap['msg'] ?? 'Unknown error');
          controller.close();
          return;
        }

        // 提取data字段
        if (jsonMap.containsKey('data')) {
          String data = jsonMap['data'] ?? '';
          controller.add(data);
        }
      } catch (e) {
        debugPrint('JSON parse error: $e, data: $jsonData');
      }
    }
  } catch (e) {
    debugPrint('Chunk processing error: $e');
  }
}

/// 从缓冲区中提取完整的 JSON 数据
List<String> _extractJsonFromBuffer(String buffer, RegExp dataJsonRegex) {
  List<String> results = [];

  // 查找所有匹配项
  Iterable<Match> matches = dataJsonRegex.allMatches(buffer);

  for (Match match in matches) {
    if (match.groupCount >= 1) {
      String jsonStr = match.group(1);
      results.add(jsonStr);
    }
  }

  return results;
}

/// 清理流式请求的 Dio 资源
void _disposeStreamDio(Dio dio) {
  if (dio != null) {
    dio.close(force: true);
  }
}

/// 格式化时间为易读格式
String _formatTime(DateTime dateTime) {
  return '${dateTime.year}-${_pad(dateTime.month)}-${_pad(dateTime.day)} '
      '${_pad(dateTime.hour)}:${_pad(dateTime.minute)}:${_pad(dateTime.second)}.'
      '${dateTime.millisecond.toString().padLeft(3, '0')}';
}

/// 数字补零
String _pad(int number) {
  return number.toString().padLeft(2, '0');
}
